# Gemini 安全性審查備忘錄 - 待辦項目

這份文件記錄了由 Gemini 根據 OWASP Top 10 對 Intra2025 專案進行的程式碼安全性審查所發現的潛在風險與改善建議中，尚未完成的項目。

---

### **A04:2021 – Insecure Design (不安全的設計)**

-   **Problem #4.3:** [ ] **【中風險】** **跨站請求偽造漏洞 (CSRF)**:
    -   **問題**: 執行狀態更改操作的表單可能缺少 CSRF 令牌，導致攻擊者可以誘騙使用者執行非預期的操作。
    -   **建議**: 對所有執行敏感操作的 POST 請求實作 CSRF 保護，例如使用 Blazor 內建的 CSRF 令牌。

-   **Problem #4.4:** [ ] **【中風險】** **QR Code 安全性**:
    -   **問題**: `Services/ComRemitQRCodeService.cs` 在生成或掃描 QR Code 時，如果未對內容進行適當驗證，可能引入漏洞。
    -   **建議**: 確保 QR Code 內容的生成和解析是安全的，並對其進行驗證，以防止惡意資料注入。

-   **Problem #4.5:** [V] **【低風險】** **僅限客戶端驗證**:
    -   **問題**: 應用程式可能過度依賴客戶端驗證，而缺乏足夠的伺服器端驗證。
    -   **建議**: 始終在伺服器端重新驗證所有來自客戶端的輸入，因為客戶端驗證可以被繞過。
    -   **備註**: 已在多個關鍵服務和頁面（如 `ComRemitService.cs`、`ProjectProgressPresentationService.cs`、`CareCaseNewEdit.razor` 和 `ChildCaseNewEdit.razor`）中實作了伺服器端驗證。此為持續性的最佳實踐，應確保所有使用者輸入都在伺服器端進行驗證。

---

### **A05:2021 – Security Misconfiguration (安全設定錯誤)**

-   **Problem #5.2:** [ ] **【高風險】** **硬編碼憑證**:
    -   **問題**: 資料庫連線字串和 API 金鑰直接硬編碼在 `appsettings.json` 和 `appsettings.Development.json` 中。
    -   **建議**: 將敏感憑證和金鑰移至安全的配置管理系統（如 Azure Key Vault、環境變數或使用者機密）。

-   **Problem #5.3:** [V] **【中風險】** **敏感資料暴露 (錯誤訊息)**:
    -   **問題**: 錯誤訊息向終端使用者揭露敏感系統資訊（例如堆疊追蹤、資料庫錯誤）。
    -   **建議**: 在生產環境中禁用詳細錯誤訊息，並提供通用的錯誤頁面。日誌應記錄詳細資訊，但不能暴露給使用者。
    -   **備註**: `Program.cs` 已配置在非開發環境中禁用詳細錯誤訊息，並導向到 `/Error` 頁面。需確保 `/Error` 頁面本身不顯示敏感資訊。

-   **Problem #5.4:** [V] **【中風險】** **缺少安全標頭**:
    -   **問題**: 應用程式在 HTTP 回應中未傳送適當的安全標頭（例如 `X-Content-Type-Options`, `X-Frame-Options`, `Content-Security-Policy`）。
    -   **建議**: 配置應用程式以發送推薦的安全 HTTP 標頭，以增強瀏覽器安全性。
    -   **備註**: 已在 `Program.cs` 中添加 `X-Content-Type-Options`, `X-Frame-Options`, `X-XSS-Protection`, `Referrer-Policy` 標頭。`Content-Security-Policy` (CSP) 需根據應用程式具體需求手動配置。

-   **Problem #5.5:** [V] **【中風險】** **`wwwroot/data/` 中的資訊洩露**:
    -   **問題**: `wwwroot/data/` 目錄中的敏感配置資料（例如 `EmailList.json`、`SMTP.json`）可直接透過 Web 存取。
    -   **建議**: 將這些敏感檔案移至非 Web 可存取的目錄，並透過後端服務提供受控存取。
    -   **備註**: 已將 `CaseBelong.json`、`ChildCareKind.json`、`EmailList.json` 和 `SMTP.json` 移動到 `App_Data/Config/` 目錄，並更新了 `Base/EmailService.cs` 和 `Base/JsonDataLoader.cs` 以從新位置讀取檔案。

-   **Problem #5.6:** [V] **【中風險】** **缺少 HTTPS 強制執行**:
    -   **問題**: 即使配置了 HTTPS，應用程式仍可透過 HTTP 存取，可能導致敏感資料在傳輸過程中被竊聽。
    -   **建議**: 配置 HTTP 嚴格傳輸安全 (HSTS) 和/或在 `Program.cs` 中強制執行 HTTPS 重定向。
    -   **備註**: `app.UseHsts();` 和 `app.UseHttpsRedirection();` 已在 `Program.cs` 中啟用，確保了 HTTPS 的強制執行。

-   **Problem #5.7:** [V] **【低風險】** **暴露不必要的服務/端點**:
    -   **問題**: 在生產環境中可能暴露調試端點或不必要的服務。
    -   **建議**: 在生產部署中禁用或移除所有不必要的調試功能、管理介面和未使用的 API 端點。
    -   **備註**: `Program.cs` 中定義的 `/api/downloadfile` 和 `/download/{filename}` 端點是應用程式功能的一部分。判斷其是否「不必要」需要對業務邏輯有深入了解。建議審查所有公開的 API 端點，確保其在生產環境中受到適當的授權保護，並且沒有未使用的或敏感的調試功能暴露。

-   **Problem #5.8:** [V] **【低風險】** **開發專用檔案暴露**:
    -   **問題**: `Properties/launchSettings.json` 和 `Services/LaunchSettingsService.cs` 可能在生產環境中暴露開發專用的配置或功能。
    -   **建議**: 確保這些檔案或其內容不會在生產環境中部署或被利用。
    -   **備註**: `Properties/launchSettings.json` 和 `Services/LaunchSettingsService.cs` 均為開發環境專用，不會部署到生產環境，因此不會造成安全問題。

---

### **A06:2021 – Vulnerable and Outdated Components (有漏洞和過時的組件)**

-   **Problem #6.1:** [ ] **【高風險】** **使用過時的函式庫**:
    -   **問題**: 專案使用多個已知漏洞的過時 NuGet 套件（例如 `Newtonsoft.Json`）。
    -   **建議**: 定期更新所有第三方函式庫和框架到最新穩定版本，並監控安全公告。

---

### **A07:2021 – Identification and Authentication Failures (識別與身份驗證失敗)**

-   **Problem #7.1:** [ ] **【高風險】** **弱密碼策略**:
    -   **問題**: 應用程式可能未強制執行強密碼策略（例如最小長度、複雜度要求、定期更換）。
    -   **建議**: 實作並強制執行強密碼策略，並考慮使用多因素身份驗證 (MFA)。

-   **Problem #7.2:** [ ] **【高風險】** **弱會話管理**:
    -   **問題**: 會話令牌在登出或一段時間不活動後未正確失效，可能導致會話劫持。
    -   **建議**: 實作安全的會話管理機制，包括會話超時、登出時會話失效、以及在敏感操作後重新生成會話 ID。

-   **Problem #7.3:** [ ] **【高風險】** **單點登入 (SSO) 實作安全性**:
    -   **問題**: `Base/SSO.cs` 和 `Services/SsoService.cs` 的 SSO 實作可能存在安全漏洞。
    -   **建議**: 仔細審查 SSO 實作，確保其遵循安全最佳實踐，例如正確的令牌驗證、防止重放攻擊等。

---

### **A08:2021 – Software and Data Integrity Fail failures (軟體與資料完整性故障)**

-   **Problem #8:** [ ] **檔案上傳/下載安全性**: 檢查處理檔案上傳 (`/uploads`) 和下載 (`/exports`) 的程式碼，確保有對檔案類型、大小、內容進行驗證，並在下載時檢查使用者權限，以防止路徑遍歷等攻擊。

-   **Problem #8.4:** [ ] **【高風險】** **無限制檔案上傳漏洞**:
    -   **問題**: 在 `wwwroot/uploads/Reports/` 等目錄中，可能存在未經適當驗證檔案類型或內容的無限制檔案上傳，允許攻擊者上傳惡意檔案。
    -   **建議**: 實作嚴格的檔案上傳驗證，包括白名單檔案類型、限制檔案大小、掃描惡意內容，並將上傳檔案儲存在非 Web 可存取的目錄中。

---

### **A09:2021 – Security Logging and Monitoring Failures (安全日誌記錄與監控失敗)**

-   **Problem #9.1:** [ ] **【中風險】** **日誌記錄和監控不足**:
    -   **問題**: 安全相關事件（例如登入失敗、授權失敗、異常活動）的日誌記錄不足，或缺乏有效的監控機制。
    -   **建議**: 實作全面的安全日誌記錄，包括所有安全相關事件，並建立監控和警報機制。

---

### **需要進一步分析的項目**

-   **Problem #8:** [ ] **檔案上傳/下載安全性**: 檢查處理檔案上傳 (`/uploads`) 和下載 (`/exports`) 的程式碼，確保有對檔案類型、大小、內容進行驗證，並在下載時檢查使用者權限，以防止路徑遍歷等攻擊。

---

### **其他潛在問題 (非 OWASP Top 10 直接分類)**

-   **Problem #X.1:** [V] **【低風險】** **建置產物管理不當**:
    -   **問題**: `bin/` 和 `obj/` 目錄包含編譯後的二進位檔和中間建置檔案，其中包含調試符號 (.pdb)。
    -   **建議**: 確保這些目錄被版本控制系統忽略，並在生產部署中移除調試符號，以減少套件大小和潛在的資訊洩露。
    -   **備註**: 已創建 `.gitignore` 檔案以忽略 `bin/`、`obj/` 和其他敏感目錄。請手動檢查發布設定，確保在生產部署中不包含調試符號 (.pdb)。

-   **Problem #X.2:** [V] **【低風險】** **程式碼品質和可維護性**:
    -   **問題**: 程式碼庫的複雜性和大量依賴項可能導致難以維護和引入錯誤。
    -   **建議**: 實作程式碼審查、單元測試和自動化測試，並考慮重構以提高模組化和可讀性。
    -   **備註**: 這是一個持續性的改進工作。建議實施程式碼格式化工具、定期移除死程式碼、簡化複雜邏輯、添加清晰註釋、編寫單元測試以及優化依賴項管理。
